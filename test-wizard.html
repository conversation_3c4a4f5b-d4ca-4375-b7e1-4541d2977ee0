<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Wizard Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        /* Test CSS for horizontal wizard */
        .wizard-horizontal .bs-stepper-header {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .wizard-horizontal .bs-stepper-content {
            width: 100%;
            padding-top: 1rem;
        }

        .wizard-horizontal .line {
            flex: 1;
            height: 2px;
            background-color: #dee2e6;
            margin: 0 1rem;
        }

        .wizard-horizontal .step.active ~ .line,
        .wizard-horizontal .step.completed ~ .line {
            background-color: #0d6efd;
        }

        .bs-stepper-circle {
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #6c757d;
            font-weight: 600;
        }

        .step.active .bs-stepper-circle {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .step.completed .bs-stepper-circle {
            background-color: #198754;
            border-color: #198754;
            color: white;
        }

        .template-card {
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background-color: white;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .template-card:hover {
            border-color: #0d6efd;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .template-card.selected {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
        }

        .template-title {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .template-description {
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            flex-grow: 1;
            line-height: 1.4;
        }

        .template-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: auto;
        }

        .template-actions .btn {
            flex: 1;
        }

        @media (max-width: 768px) {
            .wizard-horizontal .bs-stepper-header {
                flex-direction: column;
                align-items: stretch;
            }

            .wizard-horizontal .step {
                margin-bottom: 1rem;
            }

            .wizard-horizontal .line {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test Wizard Horizontal Layout</h2>
        
        <!-- Horizontal Wizard -->
        <div id="wizard-create-document" class="bs-stepper wizard-horizontal mt-2">
            <div class="bs-stepper-header gap-lg-3">
                <!-- Step 1 -->
                <div class="step active" data-target="#contract-type-step">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-circle">1</span>
                        <span class="bs-stepper-label ms-2">
                            <span class="bs-stepper-title">Loại hợp đồng</span>
                        </span>
                    </button>
                </div>
                <div class="line"></div>
                
                <!-- Step 2 -->
                <div class="step" data-target="#template-step">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-circle">2</span>
                        <span class="bs-stepper-label ms-2">
                            <span class="bs-stepper-title">Template</span>
                        </span>
                    </button>
                </div>
                <div class="line"></div>
                
                <!-- Step 3 -->
                <div class="step" data-target="#parties-step">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-circle">3</span>
                        <span class="bs-stepper-label ms-2">
                            <span class="bs-stepper-title">Đương sự</span>
                        </span>
                    </button>
                </div>
                <div class="line"></div>
                
                <!-- Step 4 -->
                <div class="step" data-target="#assets-step">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-circle">4</span>
                        <span class="bs-stepper-label ms-2">
                            <span class="bs-stepper-title">Tài sản</span>
                        </span>
                    </button>
                </div>
                <div class="line"></div>
                
                <!-- Step 5 -->
                <div class="step" data-target="#review-step">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-circle">5</span>
                        <span class="bs-stepper-label ms-2">
                            <span class="bs-stepper-title">Xem lại</span>
                        </span>
                    </button>
                </div>
            </div>

            <div class="bs-stepper-content">
                <!-- Step 1 Content -->
                <div id="contract-type-step" class="content">
                    <h4>Chọn loại hợp đồng</h4>
                    <select class="form-select">
                        <option>Hợp đồng mua bán nhà đất</option>
                        <option>Hợp đồng cho thuê</option>
                        <option>Hợp đồng vay tiền</option>
                    </select>
                </div>

                <!-- Step 2 Content -->
                <div id="template-step" class="content" style="display: none;">
                    <h4>Chọn template</h4>
                    <div class="row g-4">
                        <div class="col-md-6 col-lg-4">
                            <div class="template-card">
                                <div class="template-title">Template mặc định</div>
                                <div class="template-description">Template chuẩn cho hợp đồng mua bán nhà đất</div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-outline-info">Preview</button>
                                    <button class="btn btn-sm btn-primary">Chọn</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="template-card selected">
                                <div class="template-title">Template nâng cao</div>
                                <div class="template-description">Template với nhiều trường thông tin chi tiết</div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-outline-info">Preview</button>
                                    <button class="btn btn-sm btn-primary">Chọn</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
