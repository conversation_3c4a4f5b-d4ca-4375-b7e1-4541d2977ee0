/**
 * Documents Wizard
 */

'use strict';

$(document).ready(function() {
  // Global variables
  let validationStepper;
  let currentTemplateFields = [];
  let partiesCount = 0;
  let assetsCount = 0;

  // Initialize wizard
  const $wizardCreateDocument = $('#wizard-create-document');
  if ($wizardCreateDocument.length) {
    // Wizard form
    const $wizardCreateDocumentForm = $('#wizard-create-document-form');

    // Wizard steps
    const $wizardStep1 = $('#contract-type-step');
    const $wizardStep2 = $('#template-step');
    const $wizardStep3 = $('#parties-step');
    const $wizardStep4 = $('#assets-step');
    const $wizardStep5 = $('#review-step');

    // Wizard navigation buttons
    const $wizardNext = $('.btn-next');
    const $wizardPrev = $('.btn-prev');

    // Initialize stepper
    validationStepper = new Stepper($wizardCreateDocument[0], {
      linear: true
    });

    // Step 1: Contract Type validation
    const FormValidation1 = FormValidation.formValidation($wizardStep1[0], {
      fields: {
        contract_type_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn loại hợp đồng'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      loadTemplatesByContractType();
      validationStepper.next();
    });

    // Step 2: Template validation
    const FormValidation2 = FormValidation.formValidation($wizardStep2[0], {
      fields: {
        asset_template_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn template'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      loadTemplateFields();
      validationStepper.next();
    });

    // Step 3: Parties validation
    const FormValidation3 = FormValidation.formValidation($wizardStep3[0], {
      fields: {},
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      if (validateParties()) {
        validationStepper.next();
      }
    });

    // Step 4: Assets validation
    const FormValidation4 = FormValidation.formValidation($wizardStep4[0], {
      fields: {},
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      if (validateAssets()) {
        generateReviewSummary();
        validationStepper.next();
      }
    });

    // Step 5: Final validation and submission
    const FormValidation5 = FormValidation.formValidation($wizardStep5[0], {
      fields: {
        title: {
          validators: {
            notEmpty: {
              message: 'Vui lòng nhập tiêu đề hồ sơ'
            }
          }
        },
        confirm_document: {
          validators: {
            notEmpty: {
              message: 'Vui lòng xác nhận thông tin'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      submitDocument();
    });

    // Navigation event handlers
    $wizardNext.on('click', function(event) {
      switch (validationStepper._currentIndex) {
        case 0:
          FormValidation1.validate();
          break;
        case 1:
          FormValidation2.validate();
          break;
        case 2:
          FormValidation3.validate();
          break;
        case 3:
          FormValidation4.validate();
          break;
        case 4:
          FormValidation5.validate();
          break;
        default:
          break;
      }
    });

    $wizardPrev.on('click', function(event) {
      switch (validationStepper._currentIndex) {
        case 4:
        case 3:
        case 2:
        case 1:
          validationStepper.previous();
          break;
        case 0:
        default:
          break;
      }
    });

    // Initialize Select2 for contract type
    const contractTypeSelect = $('#contractType');
    if (contractTypeSelect.length) {
      console.log('Initializing Select2 for contract type');
      contractTypeSelect.select2({
        placeholder: 'Chọn loại hợp đồng',
        allowClear: true,
        width: '100%'
      }).on('change', function () {
        console.log('Contract type changed:', $(this).val());
        FormValidation1.revalidateField('contract_type_id');
      });
    } else {
      console.error('Contract type select element not found');
    }

    // Event handlers
    initializeEventHandlers();
  }

  function initializeEventHandlers() {
    // Add party button
    $('#addPartyBtn').on('click', addPartyForm);

    // Add asset button
    $('#addAssetBtn').on('click', addAssetForm);

    // Party search
    $('#partySearch').on('input', debounce(searchParties, 300));

    // Asset search
    $('#assetSearch').on('input', debounce(searchAssets, 300));

    // QR Scanner buttons
    $('#qrScanPartyBtn').on('click', function() { openQRScanner('party'); });
    $('#qrScanAssetBtn').on('click', function() { openQRScanner('asset'); });
  }

  function loadTemplatesByContractType() {
    const contractTypeId = $('#contractType').val();
    console.log('Loading templates for contract type:', contractTypeId);

    if (!contractTypeId) {
      console.warn('No contract type selected');
      return;
    }

    const $container = $('#templates-container');
    $container.html('<div class="loading-container"><div class="loading-spinner"></div> Đang tải templates...</div>');

    $.ajax({
      url: `/documents/ajax/templates-by-contract-type?contract_type_id=${contractTypeId}`,
      type: 'GET',
      dataType: 'json',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(templates) {
        console.log('Templates loaded:', templates);
        $container.empty();

        if (!templates || templates.length === 0) {
          $container.html('<div class="empty-container">Không có template nào cho loại hợp đồng này</div>');
          return;
        }

        templates.forEach(template => {
          const templateCard = createTemplateCard(template);
          $container.append(templateCard);
        });
      },
      error: function(xhr, status, error) {
        console.error('Error loading templates:', {xhr, status, error});
        console.error('Response text:', xhr.responseText);
        $container.html('<div class="error-container">Có lỗi xảy ra khi tải templates: ' + error + '</div>');
      }
    });
  }

  function createTemplateCard(template) {
    console.log('Creating template card for:', template);
    const $col = $('<div class="col-md-6 col-lg-4 mb-3"></div>');

    const cardHtml = `
      <div class="template-card" data-template-id="${template.id}">
        ${template.is_default ? '<span class="badge bg-primary template-badge">Mặc định</span>' : ''}
        <div class="template-title">${template.name || 'Không có tên'}</div>
        <div class="template-description">${template.description || 'Không có mô tả'}</div>
        <div class="template-actions">
          <button type="button" class="btn btn-sm btn-outline-info" onclick="previewTemplate(${template.id})">
            <i class="ri-eye-line me-1"></i>Preview
          </button>
          <button type="button" class="btn btn-sm btn-primary select-template-btn" data-template-id="${template.id}">
            <i class="ri-check-line me-1"></i>Chọn
          </button>
        </div>
        <input type="radio" name="asset_template_id" value="${template.id}" style="display: none;">
      </div>
    `;

    $col.html(cardHtml);

    // Add click handler for template selection
    $col.find('.select-template-btn').on('click', function() {
      console.log('Template selected:', template.id);
      selectTemplate(template.id);
    });

    // Add click handler for entire card
    $col.find('.template-card').on('click', function() {
      console.log('Template card clicked:', template.id);
      selectTemplate(template.id);
    });

    return $col[0];
  }

  function selectTemplate(templateId) {
    // Remove previous selections
    $('.template-card').removeClass('selected');

    // Select current template
    const $selectedCard = $(`[data-template-id="${templateId}"]`);
    $selectedCard.addClass('selected');

    // Set radio button value
    const $radio = $selectedCard.find('input[type="radio"]');
    $radio.prop('checked', true);

    // Trigger validation
    $radio.trigger('change');
  }

  function loadTemplateFields() {
    const templateId = $('input[name="asset_template_id"]:checked').val();
    console.log('Loading template fields for template:', templateId);

    if (!templateId) {
      console.warn('No template selected');
      return;
    }

    $.ajax({
      url: `/documents/ajax/template-fields?template_id=${templateId}`,
      type: 'GET',
      dataType: 'json',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(data) {
        console.log('Template fields loaded:', data);
        currentTemplateFields = data.grouped_fields;
        // Initialize first party and asset forms
        addPartyForm();
        addAssetForm();
      },
      error: function(xhr, status, error) {
        console.error('Error loading template fields:', {xhr, status, error});
        console.error('Response text:', xhr.responseText);
        alert('Có lỗi xảy ra khi tải thông tin template: ' + error);
      }
    });
  }

  function addPartyForm() {
    partiesCount++;
    const $container = $('#parties-container');

    const partyHtml = `
      <div class="party-item" data-party-index="${partiesCount}">
        <button type="button" class="btn btn-sm btn-outline-danger remove-btn" onclick="removeParty(${partiesCount})">
          <i class="ri-close-line"></i>
        </button>
        <h6>Đương sự #${partiesCount}</h6>
        <div class="row g-3">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <select name="parties[${partiesCount}][party_type]" class="form-select">
                <option value="party_a">Bên A</option>
                <option value="party_b">Bên B</option>
                <option value="witness">Người chứng kiến</option>
                <option value="other">Khác</option>
              </select>
              <label>Loại đương sự</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text" name="parties[${partiesCount}][full_name]" class="form-control" required>
              <label>Họ tên *</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="number" name="parties[${partiesCount}][birth_year]" class="form-control" min="1900" max="${new Date().getFullYear()}" required>
              <label>Năm sinh *</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text" name="parties[${partiesCount}][id_number]" class="form-control" required>
              <label>Số CCCD/Hộ chiếu *</label>
            </div>
          </div>
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <textarea name="parties[${partiesCount}][current_address]" class="form-control" required></textarea>
              <label>Nơi cư trú hiện tại *</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text" name="parties[${partiesCount}][phone]" class="form-control">
              <label>Số điện thoại</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="email" name="parties[${partiesCount}][email]" class="form-control">
              <label>Email</label>
            </div>
          </div>
        </div>
      </div>
    `;

    $container.append(partyHtml);
  }

  function addAssetForm() {
    assetsCount++;
    const $container = $('#assets-container');

    let fieldsHtml = '';
    if (currentTemplateFields) {
      Object.keys(currentTemplateFields).forEach(groupName => {
        fieldsHtml += `<div class="field-group">
          <div class="field-group-title">${groupName}</div>
          <div class="row g-3">`;

        currentTemplateFields[groupName].forEach(field => {
          fieldsHtml += generateFieldHtml(field, assetsCount);
        });

        fieldsHtml += '</div></div>';
      });
    }

    const assetHtml = `
      <div class="asset-item" data-asset-index="${assetsCount}">
        <button type="button" class="btn btn-sm btn-outline-danger remove-btn" onclick="removeAsset(${assetsCount})">
          <i class="ri-close-line"></i>
        </button>
        <h6>Tài sản #${assetsCount}</h6>
        <div class="row g-3 mb-3">
          <div class="col-md-8">
            <div class="form-floating form-floating-outline">
              <input type="text" name="assets[${assetsCount}][asset_name]" class="form-control" required>
              <label>Tên tài sản *</label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" name="assets[${assetsCount}][asset_code]" class="form-control">
              <label>Mã tài sản</label>
            </div>
          </div>
        </div>
        ${fieldsHtml}
      </div>
    `;

    $container.append(assetHtml);
  }

  function generateFieldHtml(field, assetIndex) {
    const fieldName = `assets[${assetIndex}][field_values][${field.name}]`;
    const isRequired = field.pivot.is_required;
    const requiredAttr = isRequired ? 'required' : '';
    const requiredLabel = isRequired ? ' *' : '';

    let html = '<div class="col-md-6">';

    switch (field.type) {
      case 'text':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="text" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'number':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="number" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'date':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="date" name="${fieldName}" class="form-control" ${requiredAttr}>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'select':
        html += `
          <div class="form-floating form-floating-outline">
            <select name="${fieldName}" class="form-select" ${requiredAttr}>
              <option value="">Chọn...</option>`;

        if (field.options) {
          Object.keys(field.options).forEach(key => {
            html += `<option value="${key}">${field.options[key]}</option>`;
          });
        }

        html += `</select>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'textarea':
        html += `
          <div class="form-floating form-floating-outline">
            <textarea name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}"></textarea>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      default:
        html += `
          <div class="form-floating form-floating-outline">
            <input type="text" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
    }

    if (field.help_text) {
      html += `<small class="text-muted">${field.help_text}</small>`;
    }

    html += '</div>';
    return html;
  }

  // Global functions for remove buttons
  window.removeParty = function(index) {
    $(`[data-party-index="${index}"]`).remove();
  };

  window.removeAsset = function(index) {
    $(`[data-asset-index="${index}"]`).remove();
  };

  function validateParties() {
    const parties = $('.party-item');
    if (parties.length === 0) {
      alert('Vui lòng thêm ít nhất một đương sự');
      return false;
    }
    return true;
  }

  function validateAssets() {
    const assets = $('.asset-item');
    if (assets.length === 0) {
      alert('Vui lòng thêm ít nhất một tài sản');
      return false;
    }
    return true;
  }

  function generateReviewSummary() {
    const $summaryContainer = $('#review-summary');
    // Implementation for generating review summary
    $summaryContainer.html('<p>Tóm tắt thông tin sẽ được hiển thị ở đây...</p>');
  }

  function submitDocument() {
    const $form = $('#wizard-create-document-form');
    const formData = $form.serialize();

    $.ajax({
      url: '/documents/store-from-wizard',
      type: 'POST',
      data: formData,
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(result) {
        if (result.success) {
          alert('Hồ sơ đã được tạo thành công!');
          window.location.href = result.redirect_url;
        } else {
          alert('Có lỗi xảy ra: ' + result.message);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi tạo hồ sơ');
      }
    });
  }

  // Utility functions
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  function searchParties(event) {
    const search = event.target.value;
    if (search.length < 2) return;

    // Implementation for searching parties
    console.log('Searching parties:', search);
  }

  function searchAssets(event) {
    const search = event.target.value;
    if (search.length < 2) return;

    // Implementation for searching assets
    console.log('Searching assets:', search);
  }

  function openQRScanner(type) {
    // Implementation for QR scanner
    console.log('Opening QR scanner for:', type);
    $('#qrScannerModal').modal('show');
  }

  // Global function for template preview
  window.previewTemplate = function(templateId) {
    console.log('Previewing template:', templateId);
    $('#templatePreviewModal').modal('show');
  };

});
