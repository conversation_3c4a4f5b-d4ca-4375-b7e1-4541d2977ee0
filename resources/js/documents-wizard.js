/**
 * Documents Wizard
 */

'use strict';

$(document).ready(function() {
  // Global variables
  let validationStepper;
  let currentTemplateFields = [];
  let partiesCount = 0;
  let assetsCount = 0;

  // Initialize wizard
  const $wizardCreateDocument = $('#wizard-create-document');
  
  // Check if wizard element exists
  if (!$wizardCreateDocument.length) {
    console.error('Wizard element not found');
    return;
  }

  console.log('Initializing wizard...');

  // Wizard form
  const $wizardCreateDocumentForm = $('#wizard-create-document-form');

  // Wizard steps - check if they exist
  const $wizardStep1 = $('#contract-type-step');
  const $wizardStep2 = $('#template-step');
  const $wizardStep3 = $('#parties-step');
  const $wizardStep4 = $('#assets-step');
  const $wizardStep5 = $('#review-step');

  // Validate step elements exist
  if (!$wizardStep1.length || !$wizardStep2.length || !$wizardStep3.length || !$wizardStep4.length || !$wizardStep5.length) {
    console.error('One or more wizard steps not found');
    return;
  }

  // Wizard navigation buttons
  const $wizardNext = $('.btn-next');
  const $wizardPrev = $('.btn-prev');

  // Check if Stepper is available
  if (typeof Stepper === 'undefined') {
    console.error('Stepper library not loaded');
    return;
  }

  // Initialize stepper with error handling
  try {
    validationStepper = new Stepper($wizardCreateDocument[0], {
      linear: true
    });
    console.log('Stepper initialized successfully');
  } catch (error) {
    console.error('Error initializing stepper:', error);
    return;
  }

  // Check if FormValidation is available
  if (typeof FormValidation === 'undefined') {
    console.error('FormValidation library not loaded');
    return;
  }

  // Step 1: Contract Type validation
  let FormValidation1;
  try {
    FormValidation1 = FormValidation.formValidation($wizardStep1[0], {
      fields: {
        contract_type_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn loại hợp đồng'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      console.log('Step 1 validation passed');
      loadTemplatesByContractType();
      if (validationStepper && typeof validationStepper.next === 'function') {
        validationStepper.next();
      }
    });
    console.log('FormValidation1 initialized');
  } catch (error) {
    console.error('Error initializing FormValidation1:', error);
    return;
  }

  // Step 2: Template validation
  let FormValidation2;
  try {
    FormValidation2 = FormValidation.formValidation($wizardStep2[0], {
      fields: {
        asset_template_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn template'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      console.log('Step 2 validation passed');
      loadTemplateFields();
      if (validationStepper && typeof validationStepper.next === 'function') {
        validationStepper.next();
      }
    });
    console.log('FormValidation2 initialized');
  } catch (error) {
    console.error('Error initializing FormValidation2:', error);
    return;
  }

  // Initialize contract type card selection
  $('.contract-type-card').on('click', function() {
    const $card = $(this);
    const contractTypeId = $card.data('contract-type-id');
    
    console.log('Contract type card clicked:', contractTypeId);
    
    // Remove selected class from all cards
    $('.contract-type-card').removeClass('selected');
    
    // Add selected class to clicked card
    $card.addClass('selected');
    
    // Update radio button
    $('input[name="contract_type_id"]').prop('checked', false);
    $card.find('input[name="contract_type_id"]').prop('checked', true);
    
    // Revalidate if FormValidation1 exists
    if (FormValidation1 && typeof FormValidation1.revalidateField === 'function') {
      FormValidation1.revalidateField('contract_type_id');
    }
  });

  // Navigation event handlers
  $wizardNext.on('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!validationStepper) {
      console.error('Stepper not initialized');
      return;
    }
    
    const currentIndex = validationStepper._currentIndex || 0;
    console.log('Next button clicked, current step:', currentIndex);
    
    try {
      switch (currentIndex) {
        case 0:
          if (FormValidation1 && typeof FormValidation1.validate === 'function') {
            FormValidation1.validate();
          } else {
            console.error('FormValidation1 not available');
          }
          break;
        case 1:
          if (FormValidation2 && typeof FormValidation2.validate === 'function') {
            FormValidation2.validate();
          } else {
            console.error('FormValidation2 not available');
          }
          break;
        default:
          console.log('Unknown step:', currentIndex);
          break;
      }
    } catch (error) {
      console.error('Error in next button handler:', error);
    }
  });

  $wizardPrev.on('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!validationStepper) {
      console.error('Stepper not initialized');
      return;
    }
    
    const currentIndex = validationStepper._currentIndex || 0;
    console.log('Previous button clicked, current step:', currentIndex);
    
    try {
      switch (currentIndex) {
        case 1:
          if (typeof validationStepper.previous === 'function') {
            validationStepper.previous();
          } else {
            console.error('Stepper previous method not available');
          }
          break;
        case 0:
        default:
          console.log('Already at first step');
          break;
      }
    } catch (error) {
      console.error('Error in previous button handler:', error);
    }
  });

  // Load templates by contract type
  function loadTemplatesByContractType() {
    const contractTypeId = $('input[name="contract_type_id"]:checked').val();
    console.log('Loading templates for contract type:', contractTypeId);
    
    if (!contractTypeId) {
      console.warn('No contract type selected');
      return;
    }

    const $container = $('#templates-container');
    $container.html('<div class="loading-container"><div class="loading-spinner"></div> Đang tải templates...</div>');

    $.ajax({
      url: `/documents/ajax/templates-by-contract-type?contract_type_id=${contractTypeId}`,
      type: 'GET',
      dataType: 'json',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(templates) {
        console.log('Templates loaded:', templates);
        $container.empty();

        if (!templates || templates.length === 0) {
          $container.html('<div class="empty-container">Không có template nào cho loại hợp đồng này</div>');
          return;
        }

        templates.forEach(template => {
          const templateCard = createTemplateCard(template);
          $container.append(templateCard);
        });
      },
      error: function(xhr, status, error) {
        console.error('Error loading templates:', {xhr, status, error});
        console.error('Response text:', xhr.responseText);
        $container.html('<div class="error-container">Có lỗi xảy ra khi tải templates: ' + error + '</div>');
      }
    });
  }

  // Create template card
  function createTemplateCard(template) {
    console.log('Creating template card for:', template);
    const $col = $('<div class="col-md-6 col-lg-4 mb-3"></div>');

    const cardHtml = `
      <div class="template-card" data-template-id="${template.id}">
        ${template.is_default ? '<span class="badge bg-primary template-badge">Mặc định</span>' : ''}
        <div class="template-title">${template.name || 'Không có tên'}</div>
        <div class="template-description">${template.description || 'Không có mô tả'}</div>
        <div class="template-actions">
          <button type="button" class="btn btn-sm btn-outline-info" onclick="previewTemplate(${template.id})">
            <i class="ri-eye-line me-1"></i>Preview
          </button>
          <button type="button" class="btn btn-sm btn-primary select-template-btn" data-template-id="${template.id}">
            <i class="ri-check-line me-1"></i>Chọn
          </button>
        </div>
        <input type="radio" name="asset_template_id" value="${template.id}" style="display: none;">
      </div>
    `;

    $col.html(cardHtml);

    // Add click handler for template selection
    $col.find('.select-template-btn').on('click', function() {
      console.log('Template selected:', template.id);
      selectTemplate(template.id);
    });

    // Add click handler for entire card
    $col.find('.template-card').on('click', function() {
      console.log('Template card clicked:', template.id);
      selectTemplate(template.id);
    });

    return $col[0];
  }

  // Select template
  function selectTemplate(templateId) {
    console.log('Selecting template:', templateId);
    
    // Remove selected class from all template cards
    $('.template-card').removeClass('selected');
    
    // Add selected class to clicked template card
    $(`.template-card[data-template-id="${templateId}"]`).addClass('selected');
    
    // Update radio button
    $('input[name="asset_template_id"]').prop('checked', false);
    $(`input[name="asset_template_id"][value="${templateId}"]`).prop('checked', true);
    
    // Revalidate if FormValidation2 exists
    if (FormValidation2 && typeof FormValidation2.revalidateField === 'function') {
      FormValidation2.revalidateField('asset_template_id');
    }
  }

  // Global function for template preview
  window.previewTemplate = function(templateId) {
    console.log('Previewing template:', templateId);
    // Implementation for template preview
  };

  function loadTemplateFields() {
    console.log('Loading template fields...');
    // Implementation for loading template fields
  }

});
