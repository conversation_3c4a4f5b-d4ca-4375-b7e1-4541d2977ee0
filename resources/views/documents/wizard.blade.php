@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', 'T<PERSON><PERSON> <PERSON>ồ sơ mới - Wizard')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/bs-stepper/bs-stepper.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite(['resources/css/documents-wizard.css'])
<style>
/* Temporary inline CSS to fix horizontal layout immediately */
.wizard-horizontal.bs-stepper {
  display: flex !important;
  flex-direction: column !important;
}

.wizard-horizontal.bs-stepper .bs-stepper-header {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  border-right: none !important;
  border-bottom: 1px solid #dee2e6 !important;
  width: 100% !important;
  min-width: auto !important;
  padding-bottom: 1rem !important;
  margin-bottom: 2rem !important;
}

.wizard-horizontal .step {
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
}

.wizard-horizontal .step-trigger {
  display: flex !important;
  align-items: center !important;
  background: none !important;
  border: none !important;
  padding: 0.5rem !important;
  text-decoration: none !important;
  color: inherit !important;
}

.wizard-horizontal .bs-stepper-label {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  margin-left: 0.5rem !important;
}

.wizard-horizontal .line {
  flex: 1 !important;
  height: 2px !important;
  background-color: #dee2e6 !important;
  margin: 0 1rem !important;
  min-height: 2px !important;
}

.wizard-horizontal .step.active ~ .line,
.wizard-horizontal .step.completed ~ .line {
  background-color: #0d6efd !important;
}

@media (max-width: 768px) {
  .wizard-horizontal .bs-stepper-header {
    flex-direction: column !important;
    align-items: stretch !important;
  }

  .wizard-horizontal .step {
    margin-bottom: 1rem !important;
  }

  .wizard-horizontal .line {
    display: none !important;
  }
}
</style>
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/bs-stepper/bs-stepper.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js'
])
@endsection

<!-- Page Script -->
@section('page-script')
@vite(['resources/js/documents-wizard.js'])
@endsection

@section('content')
<!-- Create Document Wizard -->
<div id="wizard-create-document" class="bs-stepper wizard-horizontal mt-2">
  <div class="bs-stepper-header gap-lg-3">
    <!-- Step 1: Contract Type -->
    <div class="step" data-target="#contract-type-step">
      <button type="button" class="step-trigger">
        <span class="bs-stepper-circle"><i class="ri-check-line"></i></span>
        <span class="bs-stepper-label">
          <span class="bs-stepper-number">01</span>
          <span class="d-flex flex-column gap-1 ms-2">
            <span class="bs-stepper-title">Loại hợp đồng</span>
            <span class="bs-stepper-subtitle">Chọn loại hợp đồng</span>
          </span>
        </span>
      </button>
    </div>
    <div class="line"></div>
    
    <!-- Step 2: Template -->
    <div class="step" data-target="#template-step">
      <button type="button" class="step-trigger">
        <span class="bs-stepper-circle"><i class="ri-check-line"></i></span>
        <span class="bs-stepper-label">
          <span class="bs-stepper-number">02</span>
          <span class="d-flex flex-column gap-1 ms-2">
            <span class="bs-stepper-title">Template</span>
            <span class="bs-stepper-subtitle">Chọn template</span>
          </span>
        </span>
      </button>
    </div>
    <div class="line"></div>
    
    <!-- Step 3: Parties -->
    <div class="step" data-target="#parties-step">
      <button type="button" class="step-trigger">
        <span class="bs-stepper-circle"><i class="ri-check-line"></i></span>
        <span class="bs-stepper-label">
          <span class="bs-stepper-number">03</span>
          <span class="d-flex flex-column gap-1 ms-2">
            <span class="bs-stepper-title">Đương sự</span>
            <span class="bs-stepper-subtitle">Thông tin đương sự</span>
          </span>
        </span>
      </button>
    </div>
    <div class="line"></div>
    
    <!-- Step 4: Assets -->
    <div class="step" data-target="#assets-step">
      <button type="button" class="step-trigger">
        <span class="bs-stepper-circle"><i class="ri-check-line"></i></span>
        <span class="bs-stepper-label">
          <span class="bs-stepper-number">04</span>
          <span class="d-flex flex-column gap-1 ms-2">
            <span class="bs-stepper-title">Tài sản</span>
            <span class="bs-stepper-subtitle">Thông tin tài sản</span>
          </span>
        </span>
      </button>
    </div>
    <div class="line"></div>
    
    <!-- Step 5: Review -->
    <div class="step" data-target="#review-step">
      <button type="button" class="step-trigger">
        <span class="bs-stepper-circle"><i class="ri-check-line"></i></span>
        <span class="bs-stepper-label">
          <span class="bs-stepper-number">05</span>
          <span class="d-flex flex-column gap-1 ms-2">
            <span class="bs-stepper-title">Xem lại</span>
            <span class="bs-stepper-subtitle">Kiểm tra & xuất file</span>
          </span>
        </span>
      </button>
    </div>
  </div>

  <div class="bs-stepper-content">
    <form id="wizard-create-document-form" method="POST" action="{{ route('documents.store-from-wizard') }}">
      @csrf
      
      <!-- Step 1: Contract Type -->
      <div id="contract-type-step" class="content">
        <div class="content-header mb-4">
          <h4 class="mb-0">Chọn loại hợp đồng</h4>
          <small>Vui lòng chọn loại hợp đồng phù hợp</small>
        </div>
        <div class="row g-5">
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <select id="contractType" name="contract_type_id" class="form-select">
                <option value="">Chọn loại hợp đồng</option>
                @foreach($contractTypes as $contractType)
                  <option value="{{ $contractType->id }}">{{ $contractType->name }}</option>
                @endforeach
              </select>
              <label for="contractType">Loại hợp đồng</label>
            </div>
          </div>
        </div>
        <div class="col-12 d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary btn-prev" disabled>
            <i class="ri-arrow-left-line ri-16px me-sm-1"></i>
            <span class="align-middle d-sm-inline-block d-none">Trước</span>
          </button>
          <button type="button" class="btn btn-primary btn-next">
            <span class="align-middle d-sm-inline-block d-none me-sm-1">Tiếp</span>
            <i class="ri-arrow-right-line ri-16px"></i>
          </button>
        </div>
      </div>

      <!-- Step 2: Template -->
      <div id="template-step" class="content">
        <div class="content-header mb-4">
          <h4 class="mb-0">Chọn template</h4>
          <small>Chọn template phù hợp cho loại hợp đồng</small>
        </div>
        <div class="row g-4" id="templates-container">
          <!-- Templates will be loaded here via AJAX -->
        </div>
        <div class="col-12 d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary btn-prev">
            <i class="ri-arrow-left-line ri-16px me-sm-1"></i>
            <span class="align-middle d-sm-inline-block d-none">Trước</span>
          </button>
          <button type="button" class="btn btn-primary btn-next">
            <span class="align-middle d-sm-inline-block d-none me-sm-1">Tiếp</span>
            <i class="ri-arrow-right-line ri-16px"></i>
          </button>
        </div>
      </div>

      <!-- Step 3: Parties -->
      <div id="parties-step" class="content">
        <div class="content-header mb-4">
          <h4 class="mb-0">Thông tin đương sự</h4>
          <small>Nhập thông tin các đương sự tham gia</small>
        </div>
        
        <!-- Search existing parties -->
        <div class="row mb-4">
          <div class="col-md-8">
            <div class="form-floating form-floating-outline">
              <input type="text" id="partySearch" class="form-control" placeholder="Tìm kiếm theo tên hoặc CCCD">
              <label for="partySearch">Tìm kiếm đương sự</label>
            </div>
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-outline-info w-100" id="qrScanPartyBtn">
              <i class="ri-qr-scan-line me-1"></i>
              Quét QR
            </button>
          </div>
        </div>

        <!-- Parties container -->
        <div id="parties-container">
          <!-- Parties will be added here -->
        </div>

        <div class="mb-4">
          <button type="button" class="btn btn-outline-primary" id="addPartyBtn">
            <i class="ri-add-line me-1"></i>
            Thêm đương sự
          </button>
        </div>

        <div class="col-12 d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary btn-prev">
            <i class="ri-arrow-left-line ri-16px me-sm-1"></i>
            <span class="align-middle d-sm-inline-block d-none">Trước</span>
          </button>
          <button type="button" class="btn btn-primary btn-next">
            <span class="align-middle d-sm-inline-block d-none me-sm-1">Tiếp</span>
            <i class="ri-arrow-right-line ri-16px"></i>
          </button>
        </div>
      </div>

      <!-- Step 4: Assets -->
      <div id="assets-step" class="content">
        <div class="content-header mb-4">
          <h4 class="mb-0">Thông tin tài sản</h4>
          <small>Nhập thông tin các tài sản liên quan</small>
        </div>

        <!-- Search existing assets -->
        <div class="row mb-4">
          <div class="col-md-8">
            <div class="form-floating form-floating-outline">
              <input type="text" id="assetSearch" class="form-control" placeholder="Tìm kiếm tài sản">
              <label for="assetSearch">Tìm kiếm tài sản</label>
            </div>
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-outline-info w-100" id="qrScanAssetBtn">
              <i class="ri-qr-scan-line me-1"></i>
              Quét QR tài sản
            </button>
          </div>
        </div>

        <!-- Assets container -->
        <div id="assets-container">
          <!-- Assets will be added here -->
        </div>

        <div class="mb-4">
          <button type="button" class="btn btn-outline-primary" id="addAssetBtn">
            <i class="ri-add-line me-1"></i>
            Thêm tài sản
          </button>
        </div>

        <div class="col-12 d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary btn-prev">
            <i class="ri-arrow-left-line ri-16px me-sm-1"></i>
            <span class="align-middle d-sm-inline-block d-none">Trước</span>
          </button>
          <button type="button" class="btn btn-primary btn-next">
            <span class="align-middle d-sm-inline-block d-none me-sm-1">Tiếp</span>
            <i class="ri-arrow-right-line ri-16px"></i>
          </button>
        </div>
      </div>

      <!-- Step 5: Review -->
      <div id="review-step" class="content">
        <div class="content-header mb-4">
          <h4 class="mb-0">Xem lại thông tin</h4>
          <small>Kiểm tra lại thông tin trước khi tạo hồ sơ</small>
        </div>

        <!-- Document basic info -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text" id="documentTitle" name="title" class="form-control" placeholder="Nhập tiêu đề hồ sơ">
              <label for="documentTitle">Tiêu đề hồ sơ</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <textarea id="documentDescription" name="description" class="form-control" placeholder="Mô tả hồ sơ"></textarea>
              <label for="documentDescription">Mô tả</label>
            </div>
          </div>
        </div>

        <!-- Review summary -->
        <div id="review-summary">
          <!-- Summary will be populated here -->
        </div>

        <div class="form-check form-switch mb-4">
          <input type="checkbox" class="form-check-input" id="confirmDocument" name="confirm_document" />
          <label for="confirmDocument" class="form-check-label">Tôi xác nhận thông tin trên là chính xác</label>
        </div>

        <div class="col-12 d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary btn-prev">
            <i class="ri-arrow-left-line ri-16px me-sm-1"></i>
            <span class="align-middle d-sm-inline-block d-none">Trước</span>
          </button>
          <button class="btn btn-success btn-submit" type="submit">
            <i class="ri-check-line me-1"></i>
            Tạo hồ sơ
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- QR Scanner Modal -->
<div class="modal fade" id="qrScannerModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Quét mã QR</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="qr-reader" style="width: 100%;"></div>
        <div id="qr-reader-results"></div>
      </div>
    </div>
  </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Preview Template</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="template-preview-content">
          <!-- Template preview will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
